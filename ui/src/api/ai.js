import axios from 'axios'

// 创建专用于AI服务的axios实例
const aiClient = axios.create({
  baseURL: 'http://10.0.17.102:8502',
  timeout: 60000, // AI请求可能需要更长的超时时间
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer sk-ad30e03bc6c4477c84897dae975dccb8'
  }
})

// AI服务API
export const aiApi = {
  /**
   * 将需求标题翻译成适合Git分支的英文名称
   * @param {string} title 需求标题
   * @returns {Promise<{branch_name: string}>} 返回翻译后的分支名称
   */
  translateTitleToBranchName(title) {
    const prompt = `帮我把该需求标题，转换成git分支的英文名称：
需求标题：${title}。
要求： 1、给出你推算出的最合适的那个； 2、只需要翻译好的英文分支名称，不需要"dev/"，"feature/"这部分分类部分； 2、以json纯文本字符串返回 {"branch_name": "xxxxx"}`

    return aiClient.post('/api/chat/completions', {
      model: 'SiliconFlow.Pro/deepseek-ai/DeepSeek-V3',
      stream: false,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    }).then(response => {
      try {
        // 从choices中获取消息内容
        const choices = response.data.choices;
        if (!choices || choices.length === 0) {
          throw new Error('响应中没有choices数组或为空');
        }

        const message = choices[0].message;
        let content = message.content;

        // 处理可能包含的markdown代码块标记
        if (content.includes('```json')) {
          content = content.replace(/```json\n|\n```/g, '');
        } else if (content.includes('```')) {
          content = content.replace(/```\n|\n```/g, '');
        }

        // 尝试解析JSON字符串
        const jsonMatch = content.match(/\{.*\}/s);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }

        // 如果没有找到JSON格式，返回原始响应
        return { branch_name: content.trim() };
      } catch (error) {
        console.error('解析AI响应失败:', error);
        // 解析失败时返回一个默认值
        return { branch_name: title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-') };
      }
    });
  }
}

export default aiApi
