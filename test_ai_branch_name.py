#!/usr/bin/env python3
"""
测试AI生成分支名称功能
"""

import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app.utils.ai_service import ai_service

def test_ai_branch_name_generation():
    """测试AI生成分支名称功能"""
    
    # 测试用例：不同类型的需求标题
    test_cases = [
        "用户登录功能优化",
        "修复数据导出bug",
        "新增项目管理模块",
        "系统性能优化",
        "用户权限管理重构",
        "API接口文档更新",
        "数据库连接池配置优化",
        "前端页面响应式布局改进",
        "安全漏洞修复",
        "测试用例管理功能开发"
    ]
    
    print("=" * 80)
    print("AI分支名称生成测试")
    print("=" * 80)
    print(f"AI服务地址: {ai_service.api_url}")
    print(f"认证Token: {ai_service.access_token[:20]}...")
    print("=" * 80)
    
    results = []
    
    for i, title in enumerate(test_cases, 1):
        print(f"\n【测试用例 {i}】")
        print(f"需求标题: {title}")
        print("-" * 60)
        
        try:
            # 调用AI服务生成分支名称
            branch_name = ai_service.translate_title_to_branch_name(title)
            
            print(f"✅ 生成成功")
            print(f"分支名称: {branch_name}")
            
            # 验证分支名称格式
            validation_results = validate_branch_name(branch_name)
            print(f"格式验证: {validation_results}")
            
            results.append({
                "title": title,
                "branch_name": branch_name,
                "success": True,
                "validation": validation_results
            })
            
        except Exception as e:
            print(f"❌ 生成失败: {str(e)}")
            results.append({
                "title": title,
                "branch_name": None,
                "success": False,
                "error": str(e)
            })
        
        print("-" * 60)
    
    # 输出测试总结
    print_test_summary(results)
    
    return results

def validate_branch_name(branch_name):
    """验证分支名称格式"""
    validations = {
        "非空": bool(branch_name and branch_name.strip()),
        "纯英文": all(ord(c) < 128 for c in branch_name),
        "无空格": " " not in branch_name,
        "无特殊字符": all(c.isalnum() or c in ['-', '_'] for c in branch_name),
        "长度合理": 3 <= len(branch_name) <= 50,
        "格式规范": branch_name.islower() or branch_name.replace('-', '').replace('_', '').islower()
    }
    
    all_passed = all(validations.values())
    return {
        "all_passed": all_passed,
        "details": validations
    }

def print_test_summary(results):
    """打印测试总结"""
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r["success"])
    failed_tests = total_tests - successful_tests
    
    print(f"总测试数: {total_tests}")
    print(f"成功数: {successful_tests}")
    print(f"失败数: {failed_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    if successful_tests > 0:
        print("\n✅ 成功的测试用例:")
        for i, result in enumerate([r for r in results if r["success"]], 1):
            validation_status = "✅" if result["validation"]["all_passed"] else "⚠️"
            print(f"  {i}. {result['title']} -> {result['branch_name']} {validation_status}")
    
    if failed_tests > 0:
        print("\n❌ 失败的测试用例:")
        for i, result in enumerate([r for r in results if not r["success"]], 1):
            print(f"  {i}. {result['title']} -> 错误: {result.get('error', '未知错误')}")
    
    # 格式验证统计
    if successful_tests > 0:
        print("\n📊 格式验证统计:")
        validation_stats = {}
        for result in [r for r in results if r["success"]]:
            for key, value in result["validation"]["details"].items():
                if key not in validation_stats:
                    validation_stats[key] = {"passed": 0, "total": 0}
                validation_stats[key]["total"] += 1
                if value:
                    validation_stats[key]["passed"] += 1
        
        for validation_type, stats in validation_stats.items():
            percentage = stats["passed"] / stats["total"] * 100
            status = "✅" if percentage == 100 else "⚠️" if percentage >= 80 else "❌"
            print(f"  {status} {validation_type}: {stats['passed']}/{stats['total']} ({percentage:.1f}%)")

def test_specific_title(title):
    """测试特定标题"""
    print("=" * 80)
    print(f"测试特定标题: {title}")
    print("=" * 80)
    
    try:
        branch_name = ai_service.translate_title_to_branch_name(title)
        print(f"✅ 生成成功: {branch_name}")
        
        validation = validate_branch_name(branch_name)
        print(f"格式验证: {validation}")
        
        return branch_name
    except Exception as e:
        print(f"❌ 生成失败: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 如果提供了命令行参数，测试特定标题
        title = " ".join(sys.argv[1:])
        test_specific_title(title)
    else:
        # 否则运行完整测试套件
        test_ai_branch_name_generation()
